'use client'

import { useState, useEffect } from 'react'
import { Item, testSupabaseConnection } from '@/lib/supabase'
import SearchInput from '@/components/SearchInput'
import BarcodeScanner from '@/components/BarcodeScanner'
import ItemActions from '@/components/ItemActions'
import ItemForm from '@/components/ItemForm'

type ViewMode = 'main' | 'actions' | 'create' | 'edit'

export default function Home() {
  const [viewMode, setViewMode] = useState<ViewMode>('main')
  const [selectedItem, setSelectedItem] = useState<Item | null>(null)
  const [newItemUpc, setNewItemUpc] = useState<string>('')
  const [scannerKey, setScannerKey] = useState(0) // Force scanner re-mount

  // Test Supabase connection on app load
  useEffect(() => {
    testSupabaseConnection()
  }, [])

  const handleItemSelect = (item: Item) => {
    setSelectedItem(item)
    setViewMode('actions')
  }

  const handleNewItem = (upc: string) => {
    setNewItemUpc(upc)
    setViewMode('create')
  }

  const handleScan = async (result: string) => {
    console.log('📱 Scanned:', result)

    // First check if item already exists
    try {
      const { itemsApi } = await import('@/lib/supabase')
      const existingItem = await itemsApi.getByUpc(result)

      if (existingItem) {
        // Item exists, go to actions
        handleItemSelect(existingItem)
      } else {
        // Item doesn't exist, create new
        handleNewItem(result)
      }
    } catch (error) {
      console.error('❌ Database error while checking for existing item:', error)
      alert('Database connection error. Please check your connection and try again.')
    }
  }

  const handleBackToMain = () => {
    setSelectedItem(null)
    setNewItemUpc('')
    setViewMode('main')
    // Force scanner to re-mount to prevent duplicate cameras
    setScannerKey(prev => prev + 1)
  }

  const handleItemSaved = (item: Item) => {
    setSelectedItem(item)
    setViewMode('actions')
  }

  const handleItemUpdate = (updatedItem: Item) => {
    setSelectedItem(updatedItem)
  }

  const handleEditItem = () => {
    setViewMode('edit')
  }

  const handleScannerError = (error: string) => {
    console.log('📱 Scanner error, search fallback available:', error)
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'actions':
        return selectedItem ? (
          <ItemActions
            item={selectedItem}
            onBack={handleBackToMain}
            onEdit={handleEditItem}
            onItemUpdate={handleItemUpdate}
            onTransactionComplete={handleBackToMain}
          />
        ) : null

      case 'create':
        return (
          <ItemForm
            initialUpc={newItemUpc}
            onSave={handleItemSaved}
            onCancel={handleBackToMain}
          />
        )

      case 'edit':
        return selectedItem ? (
          <ItemForm
            item={selectedItem}
            onSave={handleItemSaved}
            onCancel={() => setViewMode('actions')}
          />
        ) : null

      default: // main - combined scanner and search
        return (
          <div className="space-y-6">
            {/* Barcode Scanner - Primary Method */}
            <div className="mb-6">
              <BarcodeScanner
                key={scannerKey}
                onScan={handleScan}
                onError={handleScannerError}
              />
            </div>

            {/* Search Input - Alternative Method */}
            <div className="border-t border-gray-700 pt-6">
              <div className="mb-4 text-center">
                <span className="text-gray-400 text-sm">
                  🔍 Or search for existing items
                </span>
              </div>
              <SearchInput
                onItemSelect={handleItemSelect}
                onNewItem={handleNewItem}
                placeholder="Search existing items by name or description..."
                autoFocus={false}
              />
            </div>

            {/* Instructions */}
            <div className="bg-gray-800 border border-gray-600 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-purple-300 mb-4">
                🚀 How to Use
              </h2>
              <div className="space-y-3 text-sm text-gray-300">
                <p>
                  <strong>1. Scan Barcode:</strong> Point camera at UPC barcode (starts automatically)
                </p>
                <p>
                  <strong>2. Search Items:</strong> Type to search existing items in your database
                </p>
                <p>
                  <strong>3. Manage:</strong> Use Count/Use/Receive operations on items
                </p>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto p-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            {/* eslint-disable-next-line @next/next/no-img-element */}
            <img src="/isc-logo.svg" alt="ISC Logo" className="h-16 w-auto" />
          </div>
          <h1 className="text-3xl font-bold text-purple-400 mb-2">
            ISC Inventory Management System
          </h1>
          <p className="text-gray-400">
            Scan barcodes, search items, and manage your inventory
          </p>
        </div>

        {/* Main Content */}
        {renderContent()}

        {/* Footer */}
        <div className="mt-12 text-center text-gray-500 text-sm">
          <p>© 2024 Inventory Sales Company (ISC)</p>
          <p className="mt-1">Built with NextJS, TypeScript, and html5-qrcode</p>
        </div>
      </div>
    </div>
  )
}
