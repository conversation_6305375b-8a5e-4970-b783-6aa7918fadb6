import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Dynamic import to avoid Turbopack issues
    const nodemailer = await import('nodemailer')

    console.log('🔍 Nodemailer import structure:', Object.keys(nodemailer))
    console.log('🔍 Default export:', typeof nodemailer.default)
    console.log('🔍 createTransporter on default:', typeof nodemailer.default?.createTransporter)
    console.log('🔍 Direct createTransporter:', typeof nodemailer.createTransporter)

    const config = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || process.env.EMAIL_FROM,
        pass: process.env.SMTP_PASS
      }
    }

    console.log('🧪 Testing SMTP with dynamic import...')
    console.log('Config:', {
      host: config.host,
      port: config.port,
      secure: config.secure,
      user: config.auth.user,
      pass: config.auth.pass ? '***hidden***' : 'NOT SET'
    })

    // Create transporter
    const transporter = nodemailer.default.createTransporter(config)
    
    // Test connection
    console.log('🔗 Verifying SMTP connection...')
    await transporter.verify()
    console.log('✅ SMTP connection verified!')

    // Send test email
    const emailFrom = process.env.EMAIL_FROM || '<EMAIL>'
    const emailTo = process.env.EMAIL_TO || '<EMAIL>'

    const mailOptions = {
      from: emailFrom,
      to: emailTo,
      subject: '🧪 Simple SMTP Test - Inventory System',
      text: `This is a simple test email from the ISC Inventory Management System.

Configuration:
- Host: ${config.host}
- Port: ${config.port}
- Secure: ${config.secure}
- From: ${emailFrom}
- To: ${emailTo}

If you received this email, your Gmail SMTP configuration is working correctly!

Timestamp: ${new Date().toISOString()}`,
      html: `
        <h2>🧪 Simple SMTP Test</h2>
        <p>This is a simple test email from the <strong>ISC Inventory Management System</strong>.</p>
        
        <h3>Configuration:</h3>
        <ul>
          <li><strong>Host:</strong> ${config.host}</li>
          <li><strong>Port:</strong> ${config.port}</li>
          <li><strong>Secure:</strong> ${config.secure}</li>
          <li><strong>From:</strong> ${emailFrom}</li>
          <li><strong>To:</strong> ${emailTo}</li>
        </ul>
        
        <p style="color: green;"><strong>✅ If you received this email, your Gmail SMTP configuration is working correctly!</strong></p>
        
        <hr>
        <p style="font-size: 12px; color: #666;">
          <strong>Timestamp:</strong> ${new Date().toISOString()}<br>
          <strong>System:</strong> ISC Inventory Management System
        </p>
      `
    }

    console.log('📧 Sending test email...')
    const info = await transporter.sendMail(mailOptions)
    console.log('✅ Test email sent successfully!')
    console.log('Message ID:', info.messageId)

    return NextResponse.json({
      success: true,
      message: 'SMTP test completed successfully',
      details: {
        connectionVerified: true,
        emailSent: true,
        messageId: info.messageId,
        config: {
          host: config.host,
          port: config.port,
          secure: config.secure,
          from: emailFrom,
          to: emailTo
        }
      }
    })

  } catch (error) {
    console.error('❌ SMTP test failed:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: {
        connectionVerified: false,
        emailSent: false
      }
    }, { status: 500 })
  }
}
