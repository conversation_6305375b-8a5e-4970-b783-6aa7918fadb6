import { NextRequest, NextResponse } from 'next/server'
import { testSMTPConnection, sendTestEmail, checkEmailEnvironment } from '@/lib/smtp-test'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const testType = searchParams.get('type') || 'connection'

    switch (testType) {
      case 'connection':
        const connectionResult = await testSMTPConnection()
        return NextResponse.json({
          test: 'SMTP Connection',
          success: connectionResult.success,
          result: connectionResult
        })

      case 'email':
        const emailResult = await sendTestEmail()
        return NextResponse.json({
          test: 'Send Test Email',
          success: emailResult.success,
          result: emailResult
        })

      case 'environment':
        const envCheck = checkEmailEnvironment()
        return NextResponse.json({
          test: 'Environment Variables',
          success: envCheck,
          result: { environmentValid: envCheck }
        })

      case 'full':
        // Run all tests
        const envResult = checkEmailEnvironment()
        const connResult = await testSMTPConnection()
        const mailResult = connResult.success ? await sendTestEmail() : null

        return NextResponse.json({
          test: 'Full Test Suite',
          success: envResult && connResult.success && (mailResult?.success || false),
          results: {
            environment: { success: envResult },
            connection: connResult,
            email: mailResult
          }
        })

      default:
        return NextResponse.json(
          { error: 'Invalid test type. Use: connection, email, environment, or full' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('❌ Email test API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { testType = 'email', customConfig } = body

    if (testType === 'custom' && customConfig) {
      // Test with custom SMTP configuration
      const nodemailer = await import('nodemailer')

      try {
        const transporter = nodemailer.createTransporter(customConfig)
        await transporter.verify()
        
        return NextResponse.json({
          test: 'Custom SMTP Configuration',
          success: true,
          message: 'Custom SMTP configuration is valid'
        })
      } catch (error) {
        return NextResponse.json({
          test: 'Custom SMTP Configuration',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Default: send test email
    const result = await sendTestEmail()
    return NextResponse.json({
      test: 'Send Test Email (POST)',
      success: result.success,
      result
    })

  } catch (error) {
    console.error('❌ Email test POST API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
