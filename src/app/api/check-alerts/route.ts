import { NextRequest, NextResponse } from 'next/server'
import { itemsApi } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { itemUpc } = body

    if (!itemUpc) {
      return NextResponse.json(
        { error: 'Item UPC is required' },
        { status: 400 }
      )
    }

    console.log(`📧 API: Checking alerts for item UPC: ${itemUpc}`)

    // Get the item from the database
    const item = await itemsApi.getByUpc(itemUpc)
    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      )
    }

    console.log(`📧 API: Found item: ${item.description}`)

    // Import and run email alerts
    const { emailAlerts } = await import('@/lib/email')
    await emailAlerts.checkAndSendAlerts(item)

    return NextResponse.json({
      success: true,
      message: 'Email alerts checked successfully',
      item: {
        upc: item.upc,
        description: item.description,
        quantity: item.quantity,
        minimum: item.minimum
      }
    })

  } catch (error) {
    console.error('❌ Check alerts API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
