'use client'

import { useState } from 'react'
import { Item, transactionsApi } from '@/lib/supabase'

interface ItemActionsProps {
  item: Item
  onBack: () => void
  onEdit: () => void
  onItemUpdate?: (updatedItem: Item) => void
  onTransactionComplete?: () => void
}

export default function ItemActions({ item, onBack, onEdit, onItemUpdate, onTransactionComplete }: ItemActionsProps) {
  const [quantity, setQuantity] = useState(item.quantity?.toString() || '')  // Changed from current_quantity
  const [notes, setNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [lastAction, setLastAction] = useState<string | null>(null)
  const [currentItem, setCurrentItem] = useState<Item>(item)

  const calculateNewQuantity = (type: 'count' | 'use' | 'receive', qty: number): number => {
    const currentQty = currentItem.quantity || 0  // Changed from current_quantity

    switch (type) {
      case 'count':
        return qty // Count sets the exact quantity
      case 'use':
        return Math.max(0, currentQty - qty) // Use removes from current quantity
      case 'receive':
        return currentQty + qty // Receive adds to current quantity
      default:
        return currentQty
    }
  }

  const getTransactionPreview = (type: 'count' | 'use' | 'receive', qty: number) => {
    const currentQty = currentItem.quantity || 0  // Changed from current_quantity
    const newQty = calculateNewQuantity(type, qty)

    switch (type) {
      case 'count':
        return `Set quantity to ${newQty}`
      case 'use':
        return `${currentQty} - ${qty} = ${newQty}`
      case 'receive':
        return `${currentQty} + ${qty} = ${newQty}`
      default:
        return ''
    }
  }

  const handleTransaction = async (type: 'count' | 'use' | 'receive') => {
    if (quantity === '' || isSubmitting) return

    const qty = parseInt(quantity)
    if (isNaN(qty) || qty < 0) {
      alert('Please enter a valid quantity (0 or greater)')
      return
    }

    // For 'use' transactions, check if we have enough quantity
    if (type === 'use' && qty > (currentItem.quantity || 0)) {
      alert(`Cannot use ${qty} items. Only ${currentItem.quantity || 0} available.`)
      return
    }

    setIsSubmitting(true)
    try {
      console.log('🔄 Starting transaction for item:', currentItem)

      const newQuantity = calculateNewQuantity(type, qty)
      let quantityChange = qty
      if (type === 'use') quantityChange = -qty

      const transactionData = {
        upc: currentItem.upc,  // Changed from item_id to upc
        transaction_type: type,
        quantity_change: quantityChange,
        new_quantity: newQuantity,
        notes: notes.trim() || undefined
      }

      console.log('🔄 Transaction data:', transactionData)

      const result = await transactionsApi.create(transactionData)

      // Update the current item state with the updated item from the database
      setCurrentItem(result.updatedItem)
      if (onItemUpdate) {
        onItemUpdate(result.updatedItem)
      }

      const preview = getTransactionPreview(type, qty)
      setLastAction(`${type.toUpperCase()}: ${preview}${notes ? ` (${notes})` : ''}`)

      // Reset quantity to new current quantity for next transaction
      setQuantity(result.updatedItem.quantity?.toString() || '')  // Changed from current_quantity
      setNotes('')

      // After successful transaction, return to scanner
      setTimeout(() => {
        if (onTransactionComplete) {
          onTransactionComplete()
        }
      }, 2000) // Give user time to see the success message
    } catch (error) {
      console.error('Transaction error:', error)
      alert('Failed to record transaction. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const actionButtons = [
    {
      type: 'count' as const,
      label: '📊 Count',
      description: 'Set current quantity',
      color: 'bg-blue-600 hover:bg-blue-700',
      icon: '📊'
    },
    {
      type: 'use' as const,
      label: '📤 Use',
      description: 'Remove items from inventory',
      color: 'bg-red-600 hover:bg-red-700',
      icon: '📤'
    },
    {
      type: 'receive' as const,
      label: '📥 Receive',
      description: 'Add items to inventory',
      color: 'bg-green-600 hover:bg-green-700',
      icon: '📥'
    }
  ]

  return (
    <div className="max-w-2xl mx-auto p-4 bg-gray-800 rounded-lg border border-gray-600">
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={onBack}
          className="flex items-center text-purple-400 hover:text-purple-300 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back to Search
        </button>
        
        <button
          onClick={onEdit}
          className="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white text-sm rounded transition-colors"
        >
          ✏️ Edit Item
        </button>
      </div>

      <div className="mb-6 p-4 bg-gray-700 rounded border border-gray-600">
        <h2 className="text-xl font-bold text-white mb-2">
          {currentItem.suggested_brand_model || currentItem.description}
        </h2>
        {currentItem.suggested_brand_model && (
          <p className="text-gray-300 mb-2">{currentItem.description}</p>
        )}
        <div className="text-sm text-gray-400 space-y-1">
          <p><strong>UPC:</strong> {currentItem.upc}</p>
          {currentItem.quantity !== undefined && (
            <p><strong>Current Quantity:</strong> <span className="text-white font-semibold text-lg">{currentItem.quantity}</span></p>
          )}
          {currentItem.minimum && <p><strong>Minimum:</strong> {currentItem.minimum}</p>}
          {currentItem.suggested_purchase_qty && (
            <p><strong>Suggested Purchase Qty:</strong> {currentItem.suggested_purchase_qty}</p>
          )}
          {currentItem.date_last_counted && (
            <p><strong>Last Counted:</strong> {new Date(currentItem.date_last_counted).toLocaleDateString()}</p>
          )}
          {currentItem.notes && <p><strong>Notes:</strong> {currentItem.notes}</p>}
        </div>
      </div>

      {lastAction && (
        <div className="mb-4 p-3 bg-green-900 border border-green-700 text-green-300 rounded">
          <p className="text-sm">✅ <strong>Last Action:</strong> {lastAction}</p>
        </div>
      )}

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Quantity
        </label>
        <input
          type="number"
          value={quantity}
          onChange={(e) => setQuantity(e.target.value)}
          placeholder="Enter quantity..."
          min="0"
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
          autoFocus
        />

        {/* Transaction Preview */}
        {quantity !== '' && !isNaN(parseInt(quantity)) && parseInt(quantity) >= 0 && (
          <div className="mt-3 p-3 bg-gray-600 rounded border border-gray-500">
            <p className="text-sm font-medium text-gray-300 mb-2">Transaction Preview:</p>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-300">📊 Count:</span>
                <span className="text-white">{getTransactionPreview('count', parseInt(quantity))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-red-300">📤 Use:</span>
                <span className="text-white">{getTransactionPreview('use', parseInt(quantity))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-300">📥 Receive:</span>
                <span className="text-white">{getTransactionPreview('receive', parseInt(quantity))}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Notes (Optional)
        </label>
        <input
          type="text"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="Add notes..."
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {actionButtons.map((action) => (
          <button
            key={action.type}
            onClick={() => handleTransaction(action.type)}
            disabled={quantity === '' || isSubmitting}
            className={`p-4 rounded-lg text-white font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${action.color}`}
          >
            <div className="text-2xl mb-2">{action.icon}</div>
            <div className="font-bold">{action.label}</div>
            <div className="text-xs mt-1 opacity-90">{action.description}</div>
          </button>
        ))}
      </div>

      <div className="mt-6 text-xs text-gray-400 text-center">
        <p>Enter quantity and select an action above</p>
        <p><strong>Count:</strong> Set total quantity | <strong>Use:</strong> Remove from inventory | <strong>Receive:</strong> Add to inventory</p>
      </div>
    </div>
  )
}
