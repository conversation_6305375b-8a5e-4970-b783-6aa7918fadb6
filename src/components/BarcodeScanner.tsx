'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode'

interface BarcodeScannerProps {
  onScan: (result: string) => void
  onError?: (error: string) => void
}

export default function BarcodeScanner({ onScan, onError }: BarcodeScannerProps) {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [scannerError, setScannerError] = useState<string | null>(null)
  const [permissionStatus, setPermissionStatus] = useState<'requesting' | 'granted' | 'denied' | null>(null)

  // Request camera permissions with persistent access
  const requestCameraPermission = useCallback(async () => {
    try {
      setPermissionStatus('requesting')
      console.log('📷 Requesting camera permissions...')

      // Try different camera configurations for better mobile compatibility
      const constraints = [
        // First try: back camera with specific resolution
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        },
        // Fallback: back camera without resolution constraints
        {
          video: {
            facingMode: 'environment'
          }
        },
        // Last resort: any camera
        {
          video: true
        }
      ]

      let stream = null
      for (const constraint of constraints) {
        try {
          stream = await navigator.mediaDevices.getUserMedia(constraint)
          console.log('✅ Camera accessed with constraint:', constraint)
          break
        } catch (err) {
          console.log('⚠️ Failed with constraint:', constraint, err)
          continue
        }
      }

      if (!stream) {
        throw new Error('Could not access camera with any configuration')
      }

      // Stop the stream immediately - we just needed to get permission
      stream.getTracks().forEach(track => track.stop())

      setPermissionStatus('granted')
      console.log('✅ Camera permission granted')
      return true
    } catch (error) {
      console.error('❌ Camera permission denied:', error)
      setPermissionStatus('denied')
      // Only set error if it's actually denied, not just requesting
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      if (errorMessage.includes('Permission denied') || errorMessage.includes('NotAllowedError')) {
        setScannerError('Camera access is required for barcode scanning. Please allow camera access and refresh the page.')
        if (onError) {
          onError('Camera permission denied')
        }
      }
      return false
    }
  }, [onError])

  useEffect(() => {
    const initScanner = async () => {
      try {
        console.log('🎥 Initializing HTML5-QRCode scanner...')

        // Clean up any existing scanner first
        if (scannerRef.current) {
          try {
            await scannerRef.current.clear()
            scannerRef.current = null
          } catch (error) {
            console.log('Previous scanner cleanup:', error)
          }
        }

        const readerElement = document.getElementById('qr-reader')
        if (readerElement) {
          readerElement.innerHTML = ''
        }

        // Remove any existing styles
        const existingStyle = document.getElementById('qr-reader-style')
        if (existingStyle) {
          existingStyle.remove()
        }

        // First request camera permissions
        const hasPermission = await requestCameraPermission()
        if (!hasPermission) {
          return
        }

        // Ensure we don't create multiple scanners
        if (scannerRef.current) {
          console.log('Scanner already exists, skipping creation')
          return
        }

        // Detect if we're on mobile for better configuration
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

        const scanner = new Html5QrcodeScanner(
          'qr-reader',
          {
            fps: isMobile ? 5 : 10, // Lower FPS on mobile for better performance
            qrbox: isMobile ? { width: 250, height: 150 } : { width: 300, height: 200 },
            aspectRatio: 1.777778,
            supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
            showTorchButtonIfSupported: true, // Enable torch on mobile
            showZoomSliderIfSupported: true, // Enable zoom on mobile
            defaultZoomValueIfSupported: 2,
            rememberLastUsedCamera: true,
            experimentalFeatures: {
              useBarCodeDetectorIfSupported: true
            },
            // Mobile-specific camera constraints
            videoConstraints: isMobile ? {
              facingMode: 'environment',
              width: { ideal: 1280, max: 1920 },
              height: { ideal: 720, max: 1080 }
            } : undefined
          },
          false
        )

        scannerRef.current = scanner

        const onScanSuccess = (decodedText: string) => {
          console.log('🎉 Barcode scanned successfully:', decodedText)
          onScan(decodedText)

          const readerElement = document.getElementById('qr-reader')
          if (readerElement) {
            readerElement.style.border = '4px solid #10B981'
            setTimeout(() => {
              readerElement.style.border = '2px solid #7c3aed'
            }, 2000)
          }
        }

        const onScanFailure = (error: string) => {
          if (!error.includes('No QR code found') && !error.includes('NotFoundException')) {
            console.log('🔍 Scan error:', error)
          }
        }

        scanner.render(onScanSuccess, onScanFailure)
        setIsScanning(true)
        setScannerError(null)
        setPermissionStatus('granted')
        console.log('✅ Scanner started successfully')

        // Hide camera controls after scanner renders
        setTimeout(() => {
          const readerElement = document.getElementById('qr-reader')
          if (readerElement) {
            // Hide camera selection and stop button
            const existingStyle = document.getElementById('qr-reader-style')
            if (!existingStyle) {
              const style = document.createElement('style')
              style.id = 'qr-reader-style'
              style.textContent = `
                #qr-reader__dashboard_section_csr button,
                #qr-reader__dashboard_section_swaplink,
                #qr-reader__dashboard_section_fsr {
                  display: none !important;
                }
                #qr-reader__dashboard_section {
                  display: none !important;
                }
              `
              document.head.appendChild(style)
            }
          }
        }, 100)

      } catch (error) {
        console.error('❌ Scanner initialization failed:', error)
        setScannerError(error instanceof Error ? error.message : 'Scanner initialization failed')
        setIsScanning(false)
        setPermissionStatus('denied')
        if (onError) {
          onError(error instanceof Error ? error.message : 'Scanner initialization failed')
        }
      }
    }

    initScanner()

    return () => {
      console.log('🧹 Starting scanner cleanup...')

      if (scannerRef.current) {
        try {
          scannerRef.current.clear()
          scannerRef.current = null
          console.log('✅ Scanner cleared successfully')
        } catch (error) {
          console.log('⚠️ Scanner cleanup error:', error)
        }
      }

      // Clean up any remaining scanner elements
      const readerElement = document.getElementById('qr-reader')
      if (readerElement) {
        readerElement.innerHTML = ''
        console.log('✅ Scanner DOM cleaned')
      }

      // Remove any injected styles
      const style = document.getElementById('qr-reader-style')
      if (style) {
        style.remove()
        console.log('✅ Scanner styles removed')
      }

      // Stop any active video streams
      navigator.mediaDevices?.getUserMedia({ video: true })
        .then(stream => {
          stream.getTracks().forEach(track => {
            track.stop()
            console.log('✅ Video track stopped')
          })
        })
        .catch(() => {
          // Ignore errors when stopping streams
        })
    }
  }, [onScan, onError, requestCameraPermission])



  return (
    <div className="w-full max-w-2xl mx-auto p-4 bg-gray-800 rounded-lg border border-gray-600">
      <h2 className="text-xl font-bold text-white mb-4 text-center">
        📱 Barcode Scanner
      </h2>

      {permissionStatus === 'requesting' && (
        <div className="mb-4 bg-blue-900 border border-blue-700 text-blue-300 px-4 py-3 rounded">
          <p className="text-sm">
            📷 <strong>Requesting Camera Access</strong> - Please allow camera permissions when prompted
          </p>
        </div>
      )}

      {permissionStatus === 'granted' && !isScanning && (
        <div className="mb-4 bg-green-900 border border-green-700 text-green-300 px-4 py-3 rounded">
          <p className="text-sm">
            ✅ <strong>Camera Access Granted</strong> - Initializing scanner...
          </p>
        </div>
      )}

      {isScanning && (
        <div className="mb-4 bg-purple-900 border border-purple-700 text-purple-300 px-4 py-3 rounded">
          <p className="text-sm">
            🎯 <strong>Scanner Active</strong> - Point camera at barcode to scan
          </p>
          <p className="text-xs mt-1 opacity-75">
            💡 Tip: Hold your device steady and ensure good lighting. Use the torch button if available.
          </p>
        </div>
      )}

      {scannerError && permissionStatus === 'denied' && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="font-semibold">Camera Error:</p>
          <p className="text-sm">{scannerError}</p>
          <p className="text-xs mt-2">Please allow camera access and refresh the page to try again.</p>
        </div>
      )}

      <div className="mb-6">
        <div
          id="qr-reader"
          className="border-2 border-purple-600 rounded-lg overflow-hidden"
          style={{ minHeight: '300px' }}
        />
      </div>

      <div className="text-xs text-gray-400 text-center">
        <p>Point your camera at a barcode or QR code to scan</p>
      </div>
    </div>
  )
}
