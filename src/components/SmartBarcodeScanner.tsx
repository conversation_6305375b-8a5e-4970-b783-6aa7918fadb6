'use client'

import { useState, useEffect } from 'react'
import BarcodeScanner from './BarcodeScanner'
import MobileBarcodeScanner from './MobileBarcodeScanner'

interface SmartBarcodeScannerProps {
  onScan: (code: string) => void
  onError?: (error: string) => void
}

export default function SmartBarcodeScanner({ onScan, onError }: SmartBarcodeScannerProps) {
  const [isMobile, setIsMobile] = useState<boolean | null>(null)
  const [userChoice, setUserChoice] = useState<'auto' | 'desktop' | 'mobile'>('auto')

  useEffect(() => {
    // Detect mobile device
    const checkMobile = () => {
      const userAgent = navigator.userAgent.toLowerCase()
      const mobileKeywords = ['android', 'webos', 'iphone', 'ipad', 'ipod', 'blackberry', 'iemobile', 'opera mini']
      const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
      
      // Also check screen size
      const isSmallScreen = window.innerWidth <= 768
      
      // Check if touch is supported
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      
      const mobile = isMobileDevice || (isSmallScreen && isTouchDevice)
      console.log('📱 Device detection:', {
        userAgent: userAgent.substring(0, 50) + '...',
        isMobileDevice,
        isSmallScreen,
        isTouchDevice,
        finalDecision: mobile
      })
      
      setIsMobile(mobile)
    }

    checkMobile()
    
    // Re-check on resize
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  if (isMobile === null) {
    return (
      <div className="w-full max-w-2xl mx-auto p-4 bg-gray-800 rounded-lg border border-gray-600">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-2"></div>
          <p>Detecting device type...</p>
        </div>
      </div>
    )
  }

  const shouldUseMobileScanner = userChoice === 'mobile' || (userChoice === 'auto' && isMobile)

  return (
    <div className="w-full">
      {/* Scanner Type Selector */}
      <div className="mb-4 bg-gray-700 rounded-lg p-3">
        <p className="text-white text-sm mb-2">
          📱 Scanner Type: <span className="font-semibold">
            {isMobile ? 'Mobile Device Detected' : 'Desktop Device Detected'}
          </span>
        </p>
        
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setUserChoice('auto')}
            className={`px-3 py-1 rounded text-sm ${
              userChoice === 'auto' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            🤖 Auto ({isMobile ? 'Mobile' : 'Desktop'})
          </button>
          
          <button
            onClick={() => setUserChoice('desktop')}
            className={`px-3 py-1 rounded text-sm ${
              userChoice === 'desktop' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            🖥️ Desktop Scanner
          </button>
          
          <button
            onClick={() => setUserChoice('mobile')}
            className={`px-3 py-1 rounded text-sm ${
              userChoice === 'mobile' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
            }`}
          >
            📱 Mobile Scanner
          </button>
        </div>
        
        <p className="text-xs text-gray-400 mt-2">
          {shouldUseMobileScanner 
            ? '📱 Using mobile-optimized scanner with native camera controls'
            : '🖥️ Using desktop scanner with html5-qrcode library'
          }
        </p>
      </div>

      {/* Render appropriate scanner */}
      {shouldUseMobileScanner ? (
        <MobileBarcodeScanner onScan={onScan} onError={onError} />
      ) : (
        <BarcodeScanner onScan={onScan} onError={onError} />
      )}
      
      {/* Troubleshooting Tips */}
      <div className="mt-4 bg-gray-700 rounded-lg p-3">
        <details className="text-white">
          <summary className="cursor-pointer text-sm font-medium">
            🔧 Troubleshooting Tips
          </summary>
          <div className="mt-2 text-xs text-gray-300 space-y-1">
            <p><strong>No camera preview:</strong></p>
            <ul className="list-disc list-inside ml-2 space-y-1">
              <li>Try switching between Desktop and Mobile scanner modes</li>
              <li>Ensure camera permissions are granted</li>
              <li>Check if other apps are using the camera</li>
              <li>Try refreshing the page</li>
              <li>On iOS: Use Safari browser for best compatibility</li>
              <li>On Android: Chrome or Firefox work best</li>
            </ul>
            
            <p className="mt-2"><strong>Scanner not detecting barcodes:</strong></p>
            <ul className="list-disc list-inside ml-2 space-y-1">
              <li>Ensure good lighting conditions</li>
              <li>Hold the device steady</li>
              <li>Try different distances from the barcode</li>
              <li>Clean your camera lens</li>
              <li>Use the torch/flashlight if available</li>
            </ul>
            
            <p className="mt-2"><strong>Mobile-specific issues:</strong></p>
            <ul className="list-disc list-inside ml-2 space-y-1">
              <li>HTTPS is required for camera access</li>
              <li>Some browsers block camera on private/incognito mode</li>
              <li>Try landscape orientation</li>
              <li>Close other camera apps before scanning</li>
            </ul>
          </div>
        </details>
      </div>
    </div>
  )
}
