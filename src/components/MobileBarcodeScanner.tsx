'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

interface MobileBarcodeScannerProps {
  onScan: (code: string) => void
  onError?: (error: string) => void
}

export default function MobileBarcodeScanner({ onScan, onError }: MobileBarcodeScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasPermission, setHasPermission] = useState<boolean | null>(null)
  const [torchSupported, setTorchSupported] = useState(false)
  const [torchEnabled, setTorchEnabled] = useState(false)

  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        track.stop()
      })
      streamRef.current = null
    }
    setIsScanning(false)
  }, [])

  const startCamera = useCallback(async () => {
    try {
      setError(null)
      console.log('📱 Starting mobile camera...')

      // Multiple constraint configurations for better compatibility
      const constraints = [
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 1920, max: 1920 },
            height: { ideal: 1080, max: 1080 }
          }
        },
        {
          video: {
            facingMode: 'environment',
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        },
        {
          video: {
            facingMode: 'environment'
          }
        },
        {
          video: true
        }
      ]

      let stream = null
      for (const constraint of constraints) {
        try {
          console.log('📱 Trying constraint:', constraint)
          stream = await navigator.mediaDevices.getUserMedia(constraint)
          console.log('✅ Camera started with constraint:', constraint)
          break
        } catch (err) {
          console.log('⚠️ Failed with constraint:', constraint, err)
          continue
        }
      }

      if (!stream) {
        throw new Error('Could not access camera with any configuration')
      }

      streamRef.current = stream
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
        
        // Check for torch support
        const track = stream.getVideoTracks()[0]
        const capabilities = track.getCapabilities?.() as MediaTrackCapabilities & { torch?: boolean }
        if (capabilities?.torch) {
          setTorchSupported(true)
          console.log('🔦 Torch supported')
        }
      }

      setHasPermission(true)
      setIsScanning(true)
      console.log('✅ Mobile camera started successfully')

    } catch (error) {
      console.error('❌ Mobile camera error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown camera error'
      setError(`Camera error: ${errorMessage}`)
      setHasPermission(false)
      onError?.(errorMessage)
    }
  }, [onError])

  const toggleTorch = useCallback(async () => {
    if (!streamRef.current || !torchSupported) return

    try {
      const track = streamRef.current.getVideoTracks()[0]
      await track.applyConstraints({
        advanced: [{ torch: !torchEnabled } as MediaTrackConstraints]
      })
      setTorchEnabled(!torchEnabled)
      console.log(`🔦 Torch ${!torchEnabled ? 'enabled' : 'disabled'}`)
    } catch (error) {
      console.error('❌ Torch error:', error)
    }
  }, [torchEnabled, torchSupported])

  const captureFrame = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !isScanning) return

    const video = videoRef.current
    const canvas = canvasRef.current
    const context = canvas.getContext('2d')

    if (!context || video.videoWidth === 0 || video.videoHeight === 0) return

    // Set canvas size to match video
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    // Draw current video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height)

    // Try to detect barcodes using browser's built-in detector if available
    if ('BarcodeDetector' in window) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const barcodeDetector = new (window as any).BarcodeDetector()
      barcodeDetector.detect(canvas)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        .then((barcodes: any[]) => {
          if (barcodes.length > 0) {
            const barcode = barcodes[0]
            console.log('🎉 Barcode detected:', barcode.rawValue)
            onScan(barcode.rawValue)
            // Brief pause after successful scan
            setTimeout(() => {
              if (isScanning) {
                requestAnimationFrame(captureFrame)
              }
            }, 1000)
            return
          }
          // Continue scanning
          if (isScanning) {
            requestAnimationFrame(captureFrame)
          }
        })
        .catch(() => {
          // Continue scanning even if detection fails
          if (isScanning) {
            requestAnimationFrame(captureFrame)
          }
        })
    } else {
      // Fallback: just continue the capture loop for manual scanning
      if (isScanning) {
        requestAnimationFrame(captureFrame)
      }
    }
  }, [isScanning, onScan])

  useEffect(() => {
    if (isScanning && videoRef.current) {
      const video = videoRef.current
      const handleLoadedMetadata = () => {
        console.log('📱 Video metadata loaded, starting capture loop')
        captureFrame()
      }
      
      video.addEventListener('loadedmetadata', handleLoadedMetadata)
      return () => {
        video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      }
    }
  }, [isScanning, captureFrame])

  useEffect(() => {
    return () => {
      stopCamera()
    }
  }, [stopCamera])

  const handleStartScanning = () => {
    startCamera()
  }

  const handleStopScanning = () => {
    stopCamera()
  }

  return (
    <div className="w-full max-w-2xl mx-auto p-4 bg-gray-800 rounded-lg border border-gray-600">
      <h2 className="text-xl font-bold text-white mb-4 text-center">
        📱 Mobile Barcode Scanner
      </h2>

      {hasPermission === null && (
        <div className="mb-4 bg-blue-900 border border-blue-700 text-blue-300 px-4 py-3 rounded">
          <p className="text-sm">
            📷 <strong>Camera Ready</strong> - Click &quot;Start Scanning&quot; to begin
          </p>
        </div>
      )}

      {hasPermission === false && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="font-semibold">Camera Access Denied</p>
          <p className="text-sm">Please allow camera access and try again.</p>
        </div>
      )}

      {error && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="font-semibold">Error:</p>
          <p className="text-sm">{error}</p>
        </div>
      )}

      {isScanning && (
        <div className="mb-4 bg-green-900 border border-green-700 text-green-300 px-4 py-3 rounded">
          <p className="text-sm">
            🎯 <strong>Scanner Active</strong> - Point camera at barcode
          </p>
          <p className="text-xs mt-1 opacity-75">
            💡 Hold steady, ensure good lighting, and center the barcode in the frame
          </p>
        </div>
      )}

      <div className="mb-4">
        <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            style={{ transform: 'scaleX(-1)' }} // Mirror for better UX
          />
          
          {/* Scanning overlay */}
          {isScanning && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="border-2 border-red-500 bg-red-500/10 rounded-lg" 
                   style={{ width: '80%', height: '40%' }}>
                <div className="w-full h-full border-2 border-dashed border-red-300 rounded-lg animate-pulse" />
              </div>
            </div>
          )}
          
          {/* Controls overlay */}
          {isScanning && (
            <div className="absolute bottom-4 left-4 right-4 flex justify-between">
              {torchSupported && (
                <button
                  onClick={toggleTorch}
                  className={`px-3 py-2 rounded-lg text-sm font-medium ${
                    torchEnabled 
                      ? 'bg-yellow-600 text-white' 
                      : 'bg-gray-700 text-gray-300'
                  }`}
                >
                  🔦 {torchEnabled ? 'ON' : 'OFF'}
                </button>
              )}
              
              <button
                onClick={handleStopScanning}
                className="px-3 py-2 bg-red-600 text-white rounded-lg text-sm font-medium"
              >
                ⏹️ Stop
              </button>
            </div>
          )}
        </div>
      </div>

      {!isScanning && (
        <div className="text-center">
          <button
            onClick={handleStartScanning}
            className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg"
          >
            📷 Start Scanning
          </button>
        </div>
      )}

      {/* Hidden canvas for barcode detection */}
      <canvas ref={canvasRef} className="hidden" />
      
      <div className="mt-4 text-xs text-gray-400 text-center">
        <p>💡 <strong>Tips:</strong> Use good lighting, hold device steady, center barcode in red frame</p>
        {!('BarcodeDetector' in window) && (
          <p className="mt-1 text-yellow-400">
            ⚠️ Automatic detection not supported - you may need to manually enter codes
          </p>
        )}
      </div>
    </div>
  )
}
