'use client'

import { useState } from 'react'
import { Item } from '@/lib/supabase'

interface EmailTestPanelProps {
  item: Item
}

export default function EmailTestPanel({ item }: EmailTestPanelProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [lastResult, setLastResult] = useState<unknown>(null)
  const [alertInfo, setAlertInfo] = useState<unknown>(null)
  const [smtpTestResult, setSmtpTestResult] = useState<unknown>(null)

  const checkAlerts = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/send-email?upc=${item.upc}`)
      const data = await response.json()
      setAlertInfo(data)
    } catch (error) {
      console.error('Error checking alerts:', error)
      setAlertInfo({ error: 'Failed to check alerts' })
    } finally {
      setIsLoading(false)
    }
  }

  const sendAlert = async (alertType: 'low_stock' | 'cycle_count' | 'both') => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/send-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemUpc: item.upc,
          alertType
        })
      })
      const data = await response.json()
      setLastResult(data)
    } catch (error) {
      console.error('Error sending alert:', error)
      setLastResult({ error: 'Failed to send alert' })
    } finally {
      setIsLoading(false)
    }
  }

  const testSMTP = async (testType: 'connection' | 'email' | 'full') => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/test-email?type=${testType}`)
      const data = await response.json()
      setSmtpTestResult(data)
    } catch (error) {
      console.error('Error testing SMTP:', error)
      setSmtpTestResult({ error: 'Failed to test SMTP configuration' })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="bg-gray-800 border border-gray-600 rounded-lg p-4 mt-4">
      <h3 className="text-lg font-semibold text-purple-400 mb-4">📧 Email Alert Testing</h3>
      
      {/* Item Info */}
      <div className="mb-4 p-3 bg-gray-700 rounded">
        <p className="text-sm text-gray-300">
          <strong>Item:</strong> {item.description} (UPC: {item.upc})
        </p>
        <p className="text-sm text-gray-300">
          <strong>Quantity:</strong> {item.quantity || 0} | <strong>Minimum:</strong> {item.minimum || 'Not set'}
        </p>
        <p className="text-sm text-gray-300">
          <strong>Last Counted:</strong> {item.date_last_counted ? new Date(item.date_last_counted).toLocaleDateString() : 'Never'}
        </p>
      </div>

      {/* SMTP Testing Section */}
      <div className="mb-6 p-3 bg-gray-700 rounded">
        <h4 className="font-semibold text-gray-200 mb-3">🔧 SMTP Configuration Test</h4>
        <div className="space-x-2 mb-3">
          <button
            onClick={() => testSMTP('connection')}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-3 py-1 rounded text-sm"
          >
            Test Connection
          </button>
          <button
            onClick={() => testSMTP('email')}
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white px-3 py-1 rounded text-sm"
          >
            Send Test Email
          </button>
          <button
            onClick={() => testSMTP('full')}
            disabled={isLoading}
            className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white px-3 py-1 rounded text-sm"
          >
            Full Test
          </button>
        </div>

        {smtpTestResult && (
          <div className="p-2 bg-gray-600 rounded text-sm">
            <p className={`font-semibold ${(smtpTestResult as Record<string, unknown>)?.success ? 'text-green-400' : 'text-red-400'}`}>
              {(smtpTestResult as Record<string, unknown>)?.test as string}: {(smtpTestResult as Record<string, unknown>)?.success ? 'PASSED' : 'FAILED'}
            </p>
            {(smtpTestResult as Record<string, unknown>)?.error && (
              <p className="text-red-400 text-xs mt-1">{(smtpTestResult as Record<string, unknown>).error as string}</p>
            )}
            {((smtpTestResult as Record<string, unknown>)?.result as Record<string, unknown>)?.messageId && (
              <p className="text-green-400 text-xs mt-1">Message ID: {((smtpTestResult as Record<string, unknown>).result as Record<string, unknown>).messageId as string}</p>
            )}
          </div>
        )}
      </div>

      {/* Check Alerts Button */}
      <div className="mb-4">
        <button
          onClick={checkAlerts}
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white px-4 py-2 rounded mr-2"
        >
          {isLoading ? 'Checking...' : 'Check Alert Status'}
        </button>
      </div>

      {/* Alert Status */}
      {alertInfo && (
        <div className="mb-4 p-3 bg-gray-700 rounded">
          <h4 className="font-semibold text-gray-200 mb-2">Alert Status:</h4>
          {(alertInfo as Record<string, unknown>)?.error ? (
            <p className="text-red-400">{(alertInfo as Record<string, unknown>).error as string}</p>
          ) : (
            <div className="text-sm space-y-1">
              <p className={`${((alertInfo as Record<string, unknown>)?.alerts as Record<string, unknown>)?.shouldSendLowStock ? 'text-red-400' : 'text-green-400'}`}>
                <strong>Low Stock:</strong> {((alertInfo as Record<string, unknown>)?.alerts as Record<string, unknown>)?.shouldSendLowStock ? 'Alert needed' : 'No alert needed'}
              </p>
              <p className="text-gray-400 text-xs ml-4">
                {((alertInfo as Record<string, unknown>)?.alerts as Record<string, unknown>)?.lowStockReason as string}
              </p>
              <p className={`${((alertInfo as Record<string, unknown>)?.alerts as Record<string, unknown>)?.shouldSendCycleCount ? 'text-yellow-400' : 'text-green-400'}`}>
                <strong>Cycle Count:</strong> {((alertInfo as Record<string, unknown>)?.alerts as Record<string, unknown>)?.shouldSendCycleCount ? 'Alert needed' : 'No alert needed'}
              </p>
              <p className="text-gray-400 text-xs ml-4">
                {((alertInfo as Record<string, unknown>)?.alerts as Record<string, unknown>)?.cycleCountReason as string}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Send Alert Buttons */}
      <div className="mb-4">
        <h4 className="font-semibold text-gray-200 mb-2">Send Test Alerts:</h4>
        <div className="space-x-2">
          <button
            onClick={() => sendAlert('low_stock')}
            disabled={isLoading}
            className="bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white px-3 py-1 rounded text-sm"
          >
            Low Stock Alert
          </button>
          <button
            onClick={() => sendAlert('cycle_count')}
            disabled={isLoading}
            className="bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50 text-white px-3 py-1 rounded text-sm"
          >
            Cycle Count Alert
          </button>
          <button
            onClick={() => sendAlert('both')}
            disabled={isLoading}
            className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white px-3 py-1 rounded text-sm"
          >
            Both Alerts
          </button>
        </div>
      </div>

      {/* Last Result */}
      {lastResult && (
        <div className="p-3 bg-gray-700 rounded">
          <h4 className="font-semibold text-gray-200 mb-2">Last Result:</h4>
          {(lastResult as Record<string, unknown>)?.error ? (
            <p className="text-red-400">{(lastResult as Record<string, unknown>).error as string}</p>
          ) : (
            <div className="text-sm space-y-1">
              <p className="text-green-400">
                <strong>Success:</strong> {(lastResult as Record<string, unknown>)?.success ? 'Yes' : 'No'}
              </p>
              {(lastResult as Record<string, unknown>)?.results && (
                <>
                  <p className="text-gray-300">
                    <strong>Low Stock Sent:</strong> {((lastResult as Record<string, unknown>).results as Record<string, unknown>)?.lowStockSent ? 'Yes' : 'No'}
                  </p>
                  <p className="text-gray-300">
                    <strong>Cycle Count Sent:</strong> {((lastResult as Record<string, unknown>).results as Record<string, unknown>)?.cycleCountSent ? 'Yes' : 'No'}
                  </p>
                  {(((lastResult as Record<string, unknown>).results as Record<string, unknown>)?.errors as string[])?.length > 0 && (
                    <p className="text-red-400">
                      <strong>Errors:</strong> {(((lastResult as Record<string, unknown>).results as Record<string, unknown>).errors as string[]).join(', ')}
                    </p>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500">
        <p><strong>Note:</strong> This panel is for testing email functionality. In production, emails are sent automatically after transactions.</p>
      </div>
    </div>
  )
}
