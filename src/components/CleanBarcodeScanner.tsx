'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode'

interface CleanBarcodeScannerProps {
  onScan: (code: string) => void
  onError?: (error: string) => void
}

export default function CleanBarcodeScanner({ onScan, onError }: CleanBarcodeScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const scannerRef = useRef<Html5QrcodeScanner | null>(null)
  const [isScanning, setIsScanning] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [scannerError, setScannerError] = useState<string | null>(null)

  // Detect mobile device
  const detectMobile = useCallback(() => {
    const userAgent = navigator.userAgent.toLowerCase()
    const mobileKeywords = ['android', 'webos', 'iphone', 'ipad', 'ipod', 'blackberry', 'iemobile', 'opera mini']
    const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
    const isSmallScreen = window.innerWidth <= 768
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    
    return isMobileDevice || (isSmallScreen && isTouchDevice)
  }, [])

  // Clean up function
  const cleanup = useCallback(() => {
    // Stop mobile camera
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }

    // Stop desktop scanner
    if (scannerRef.current) {
      try {
        scannerRef.current.clear()
      } catch (error) {
        console.log('Scanner cleanup error (expected):', error)
      }
      scannerRef.current = null
    }

    setIsScanning(false)
  }, [])

  // Mobile scanner implementation
  const startMobileScanner = useCallback(async () => {
    try {
      console.log('📱 Starting mobile scanner...')
      
      const constraints = [
        { video: { facingMode: 'environment', width: { ideal: 1280 }, height: { ideal: 720 } } },
        { video: { facingMode: 'environment' } },
        { video: true }
      ]

      let stream = null
      for (const constraint of constraints) {
        try {
          stream = await navigator.mediaDevices.getUserMedia(constraint)
          break
        } catch {
          continue
        }
      }

      if (!stream) {
        throw new Error('Could not access camera')
      }

      streamRef.current = stream
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }

      setIsScanning(true)
      
      // Start barcode detection loop
      const detectBarcodes = () => {
        if (!videoRef.current || !canvasRef.current || !isScanning) return

        const video = videoRef.current
        const canvas = canvasRef.current
        const context = canvas.getContext('2d')

        if (!context || video.videoWidth === 0 || video.videoHeight === 0) {
          requestAnimationFrame(detectBarcodes)
          return
        }

        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        context.drawImage(video, 0, 0, canvas.width, canvas.height)

        if ('BarcodeDetector' in window) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const barcodeDetector = new (window as any).BarcodeDetector()
          barcodeDetector.detect(canvas)
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            .then((barcodes: any[]) => {
              if (barcodes.length > 0) {
                const barcode = barcodes[0]
                console.log('🎉 Mobile barcode detected:', barcode.rawValue)
                onScan(barcode.rawValue)
                setTimeout(() => {
                  if (isScanning) requestAnimationFrame(detectBarcodes)
                }, 1000)
                return
              }
              if (isScanning) requestAnimationFrame(detectBarcodes)
            })
            .catch(() => {
              if (isScanning) requestAnimationFrame(detectBarcodes)
            })
        } else {
          if (isScanning) requestAnimationFrame(detectBarcodes)
        }
      }

      // Start detection when video is ready
      if (videoRef.current && videoRef.current.readyState >= 2) {
        detectBarcodes()
      } else if (videoRef.current) {
        videoRef.current.addEventListener('loadeddata', detectBarcodes, { once: true })
      }

    } catch (error) {
      console.error('❌ Mobile scanner error:', error)
      setScannerError('Camera access failed')
      onError?.('Camera access failed')
    }
  }, [onScan, onError, isScanning])

  // Desktop scanner implementation
  const startDesktopScanner = useCallback(() => {
    try {
      console.log('🖥️ Starting desktop scanner...')
      
      const scanner = new Html5QrcodeScanner(
        'qr-reader',
        {
          fps: 10,
          qrbox: { width: 300, height: 200 },
          aspectRatio: 1.777778,
          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
          showTorchButtonIfSupported: true,
          showZoomSliderIfSupported: false,
          defaultZoomValueIfSupported: 2,
          rememberLastUsedCamera: true,
          experimentalFeatures: {
            useBarCodeDetectorIfSupported: true
          }
        },
        false
      )

      scannerRef.current = scanner

      const onScanSuccess = (decodedText: string) => {
        console.log('🎉 Desktop barcode scanned:', decodedText)
        onScan(decodedText)
      }

      const onScanFailure = () => {
        // Silent - no need to log every scan attempt
      }

      scanner.render(onScanSuccess, onScanFailure)
      setIsScanning(true)

    } catch (error) {
      console.error('❌ Desktop scanner error:', error)
      setScannerError('Scanner initialization failed')
      onError?.('Scanner initialization failed')
    }
  }, [onScan, onError])

  // Auto-start scanner on mount
  useEffect(() => {
    const mobile = detectMobile()
    setIsMobile(mobile)
    
    const startScanner = async () => {
      // Small delay to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100))
      
      if (mobile) {
        await startMobileScanner()
      } else {
        startDesktopScanner()
      }
    }

    startScanner()

    return cleanup
  }, [detectMobile, startMobileScanner, startDesktopScanner, cleanup])

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Mobile Scanner */}
      {isMobile && (
        <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '16/9' }}>
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            style={{ transform: 'scaleX(-1)' }}
          />
          
          {/* Scanning overlay */}
          {isScanning && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="border-2 border-purple-500 bg-purple-500/10 rounded-lg" 
                   style={{ width: '70%', height: '35%' }}>
                <div className="w-full h-full border-2 border-dashed border-purple-300 rounded-lg animate-pulse" />
              </div>
            </div>
          )}
          
          {/* Status indicator */}
          {isScanning && (
            <div className="absolute top-4 left-4 bg-green-600/80 text-white px-3 py-1 rounded-full text-sm">
              🎯 Scanning...
            </div>
          )}
        </div>
      )}

      {/* Desktop Scanner */}
      {!isMobile && (
        <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
          <div
            id="qr-reader"
            className="min-h-[300px]"
          />
        </div>
      )}

      {/* Error state */}
      {scannerError && (
        <div className="mt-4 bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-lg">
          <p className="text-sm">Camera access required for barcode scanning</p>
        </div>
      )}

      {/* Hidden canvas for mobile detection */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  )
}
