'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode'

interface CleanBarcodeScannerProps {
  onScan: (code: string) => void
  onError?: (error: string) => void
}

export default function CleanBarcodeScanner({ onScan, onError }: CleanBarcodeScannerProps) {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null)
  const [scannerError, setScannerError] = useState<string | null>(null)

  // Detect mobile device
  const detectMobile = useCallback(() => {
    const userAgent = navigator.userAgent.toLowerCase()
    const mobileKeywords = ['android', 'webos', 'iphone', 'ipad', 'ipod', 'blackberry', 'iemobile', 'opera mini']
    const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
    const isSmallScreen = window.innerWidth <= 768
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    return isMobileDevice || (isSmallScreen && isTouchDevice)
  }, [])

  // Clean up function
  const cleanup = useCallback(() => {
    if (scannerRef.current) {
      try {
        scannerRef.current.clear()
      } catch (error) {
        console.log('Scanner cleanup error (expected):', error)
      }
      scannerRef.current = null
    }
    // Scanner cleared
  }, [])

  // Start scanner implementation
  const startScanner = useCallback(() => {
    try {
      console.log('📱 Starting clean scanner...')

      // Detect if mobile for configuration
      const mobile = detectMobile()

      const scanner = new Html5QrcodeScanner(
        'qr-reader',
        {
          fps: mobile ? 5 : 10,
          qrbox: mobile ? { width: 250, height: 150 } : { width: 300, height: 200 },
          aspectRatio: 1.777778,
          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
          showTorchButtonIfSupported: false, // Hide torch to keep UI clean
          showZoomSliderIfSupported: false, // Hide zoom to keep UI clean
          defaultZoomValueIfSupported: 2,
          rememberLastUsedCamera: true,
          experimentalFeatures: {
            useBarCodeDetectorIfSupported: true
          },
          videoConstraints: mobile ? {
            facingMode: 'environment',
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 }
          } : undefined,
          // Auto-start configuration
          disableFlip: false
        },
        false
      )

      scannerRef.current = scanner

      const onScanSuccess = (decodedText: string) => {
        console.log('🎉 Barcode scanned:', decodedText)
        onScan(decodedText)
      }

      const onScanFailure = () => {
        // Silent - no need to log every scan attempt
      }

      scanner.render(onScanSuccess, onScanFailure)

      // Force start camera after a short delay
      setTimeout(() => {
        const startButton = document.querySelector('#qr-reader__camera_start_button') as HTMLButtonElement
        if (startButton) {
          startButton.click()
          console.log('🎬 Auto-clicked start camera button')
        }

        // Also try to click any permission request buttons
        const permissionButton = document.querySelector('#qr-reader__camera_permission_button') as HTMLButtonElement
        if (permissionButton) {
          permissionButton.click()
          console.log('🎬 Auto-clicked permission button')
        }
      }, 500)

    } catch (error) {
      console.error('❌ Scanner error:', error)
      setScannerError('Scanner initialization failed')
      onError?.('Scanner initialization failed')
    }
  }, [onScan, onError, detectMobile])

  // Request camera permission and auto-start scanner
  useEffect(() => {
    const initScanner = async () => {
      try {
        // Request camera permission first
        console.log('📷 Requesting camera permissions...')
        await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' }
        }).then(stream => {
          // Stop the permission stream immediately
          stream.getTracks().forEach(track => track.stop())
          console.log('✅ Camera permission granted')
        })

        // Small delay to ensure DOM is ready
        await new Promise(resolve => setTimeout(resolve, 300))
        startScanner()
      } catch (error) {
        console.error('❌ Camera permission denied:', error)
        setScannerError('Camera access required')
      }
    }

    initScanner()
    return cleanup
  }, [startScanner, cleanup])

  // Add custom styles to hide UI elements and monitor for changes
  useEffect(() => {
    const addCustomStyles = () => {
      const style = document.createElement('style')
      style.textContent = `
        #qr-reader {
          border: none !important;
        }
        #qr-reader__dashboard_section,
        #qr-reader__header_message,
        #qr-reader__camera_selection,
        #qr-reader__dashboard_section_csr,
        #qr-reader__status_span,
        #qr-reader__dashboard_section_swaplink,
        #qr-reader__dashboard_section_fsr,
        .qr-code-text-btn,
        [id*="__dashboard"],
        [id*="__header"],
        [id*="__camera_selection"],
        [id*="__status"] {
          display: none !important;
        }
        #qr-reader video {
          max-height: 400px !important;
          object-fit: cover !important;
          border-radius: 8px !important;
          width: 100% !important;
        }
        #qr-reader__scan_region video {
          max-height: 400px !important;
          height: auto !important;
          object-fit: cover !important;
          width: 100% !important;
        }
        #qr-reader__scan_region {
          max-height: 400px !important;
          border: none !important;
          width: 100% !important;
        }
        #qr-reader__scan_region img {
          max-height: 400px !important;
          object-fit: cover !important;
        }
        /* Hide any text content */
        #qr-reader div:not(#qr-reader__scan_region) {
          font-size: 0 !important;
        }
        #qr-reader span {
          display: none !important;
        }
        #qr-reader p {
          display: none !important;
        }
        #qr-reader button:not([id*="torch"]):not([id*="zoom"]) {
          display: none !important;
        }
      `
      document.head.appendChild(style)
      return style
    }

    const styleElement = addCustomStyles()

    // Monitor for DOM changes and hide elements
    const hideElements = () => {
      const elementsToHide = [
        '#qr-reader__dashboard_section',
        '#qr-reader__header_message',
        '#qr-reader__camera_selection',
        '#qr-reader__dashboard_section_csr',
        '#qr-reader__status_span',
        '#qr-reader__dashboard_section_swaplink',
        '#qr-reader__dashboard_section_fsr',
        '.qr-code-text-btn'
      ]

      elementsToHide.forEach(selector => {
        const elements = document.querySelectorAll(selector)
        elements.forEach(el => {
          if (el instanceof HTMLElement) {
            el.style.display = 'none'
          }
        })
      })

      // Hide any text nodes that contain instructions
      const qrReader = document.getElementById('qr-reader')
      if (qrReader) {
        const walker = document.createTreeWalker(
          qrReader,
          NodeFilter.SHOW_TEXT,
          null
        )

        let node
        while (node = walker.nextNode()) {
          if (node.textContent && (
            node.textContent.includes('Unable to access camera') ||
            node.textContent.includes('Camera based scan') ||
            node.textContent.includes('Requesting camera permissions') ||
            node.textContent.includes('Or drop an image') ||
            node.textContent.includes('Select Camera') ||
            node.textContent.toLowerCase().includes('scan')
          )) {
            if (node.parentElement) {
              node.parentElement.style.display = 'none'
            }
          }
        }
      }
    }

    // Run immediately and set up interval
    hideElements()
    const interval = setInterval(hideElements, 100)

    return () => {
      clearInterval(interval)
      if (styleElement && styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement)
      }
    }
  }, [])

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Clean Scanner Container */}
      <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
        <div
          id="qr-reader"
          className="min-h-[300px] max-h-[400px]"
        />
      </div>

      {/* Minimal error state */}
      {scannerError && (
        <div className="mt-4 bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-lg">
          <p className="text-sm">Camera access required for barcode scanning</p>
        </div>
      )}
    </div>
  )
}
