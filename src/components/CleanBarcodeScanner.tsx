'use client'

import { useEffect, useRef, useState, useCallback } from 'react'
import { Html5QrcodeScanner, Html5QrcodeScanType } from 'html5-qrcode'

interface CleanBarcodeScannerProps {
  onScan: (code: string) => void
  onError?: (error: string) => void
}

export default function CleanBarcodeScanner({ onScan, onError }: CleanBarcodeScannerProps) {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null)
  const [scannerError, setScannerError] = useState<string | null>(null)

  // Detect mobile device
  const detectMobile = useCallback(() => {
    const userAgent = navigator.userAgent.toLowerCase()
    const mobileKeywords = ['android', 'webos', 'iphone', 'ipad', 'ipod', 'blackberry', 'iemobile', 'opera mini']
    const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
    const isSmallScreen = window.innerWidth <= 768
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0

    return isMobileDevice || (isSmallScreen && isTouchDevice)
  }, [])

  // Clean up function
  const cleanup = useCallback(() => {
    if (scannerRef.current) {
      try {
        scannerRef.current.clear()
      } catch (error) {
        console.log('Scanner cleanup error (expected):', error)
      }
      scannerRef.current = null
    }
    // Scanner cleared
  }, [])

  // Start scanner implementation
  const startScanner = useCallback(() => {
    try {
      console.log('📱 Starting clean scanner...')

      // Detect if mobile for configuration
      const mobile = detectMobile()

      const scanner = new Html5QrcodeScanner(
        'qr-reader',
        {
          fps: mobile ? 5 : 10,
          qrbox: mobile ? { width: 250, height: 150 } : { width: 300, height: 200 },
          aspectRatio: 1.777778,
          supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
          showTorchButtonIfSupported: true,
          showZoomSliderIfSupported: mobile,
          defaultZoomValueIfSupported: 2,
          rememberLastUsedCamera: true,
          experimentalFeatures: {
            useBarCodeDetectorIfSupported: true
          },
          videoConstraints: mobile ? {
            facingMode: 'environment',
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 }
          } : undefined
        },
        false
      )

      scannerRef.current = scanner

      const onScanSuccess = (decodedText: string) => {
        console.log('🎉 Barcode scanned:', decodedText)
        onScan(decodedText)
      }

      const onScanFailure = () => {
        // Silent - no need to log every scan attempt
      }

      scanner.render(onScanSuccess, onScanFailure)

    } catch (error) {
      console.error('❌ Scanner error:', error)
      setScannerError('Scanner initialization failed')
      onError?.('Scanner initialization failed')
    }
  }, [onScan, onError, detectMobile])

  // Auto-start scanner on mount
  useEffect(() => {
    const initScanner = async () => {
      // Small delay to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 200))
      startScanner()
    }

    initScanner()
    return cleanup
  }, [startScanner, cleanup])

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Clean Scanner Container */}
      <div className="bg-gray-800 rounded-lg border border-gray-600 overflow-hidden">
        <div
          id="qr-reader"
          className="min-h-[300px]"
        />
      </div>

      {/* Minimal error state */}
      {scannerError && (
        <div className="mt-4 bg-red-900/50 border border-red-700 text-red-300 px-4 py-3 rounded-lg">
          <p className="text-sm">Camera access required for barcode scanning</p>
        </div>
      )}
    </div>
  )
}
