import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Item {
  id: number
  upc: string
  description: string
  quantity?: number  // Changed from current_quantity to match database
  product_image?: string
  purchase_link?: string
  suggested_brand_model?: string
  suggested_purchase_qty?: number
  minimum?: number
  date_last_counted?: string
  notes?: string
  count_interval_days?: number
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: number
  upc: string  // Changed from item_id to match database
  transaction_type: 'count' | 'use' | 'receive' | 'edit'
  quantity_change: number
  previous_quantity?: number  // Added to match database
  new_quantity: number
  notes?: string
  created_at: string
  items?: Item
}

// No mock data - using Supabase only

// API functions with Supabase integration only
export const itemsApi = {
  async search(query: string): Promise<Item[]> {
    const { data, error } = await supabase
      .from('items')
      .select('*')
      .or(`description.ilike.%${query}%,upc.ilike.%${query}%,suggested_brand_model.ilike.%${query}%`)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Supabase search failed:', error)
      throw new Error(`Search failed: ${error.message}`)
    }

    return data || []
  },

  async getByUpc(upc: string): Promise<Item | null> {
    const { data, error } = await supabase
      .from('items')
      .select('*')
      .eq('upc', upc)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows found
        return null
      }
      console.error('❌ Supabase getByUpc failed:', error)
      throw new Error(`Failed to get item by UPC: ${error.message}`)
    }

    return data
  },

  async create(item: Omit<Item, 'id' | 'created_at' | 'updated_at'>): Promise<Item> {
    const { data, error } = await supabase
      .from('items')
      .insert([item])
      .select()
      .single()

    if (error) {
      console.error('❌ Supabase create failed:', error)
      throw new Error(`Failed to create item: ${error.message}`)
    }

    return data
  },

  async update(id: number, updates: Partial<Item>): Promise<Item> {
    const { data, error } = await supabase
      .from('items')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('❌ Supabase update failed:', error)
      throw new Error(`Failed to update item: ${error.message}`)
    }

    return data
  }
}

// Test function to check database connectivity
export const testSupabaseConnection = async () => {
  try {
    console.log('🔗 Testing Supabase connection...')

    // Test items table
    const { error: itemsError } = await supabase
      .from('items')
      .select('id')
      .limit(1)

    if (itemsError) {
      console.error('❌ Items table error:', itemsError)
    } else {
      console.log('✅ Items table accessible')
    }

    // Test transactions table
    const { error: transactionsError } = await supabase
      .from('transactions')
      .select('id')
      .limit(1)

    if (transactionsError) {
      console.error('❌ Transactions table error:', transactionsError)
    } else {
      console.log('✅ Transactions table accessible')
    }

    return { itemsError, transactionsError }
  } catch (error) {
    console.error('❌ Supabase connection failed:', error)
    return { error }
  }
}

export const transactionsApi = {
  async create(transaction: Omit<Transaction, 'id' | 'created_at'>): Promise<{ transaction: Transaction; updatedItem: Item }> {
    console.log('🔄 Creating transaction:', transaction)

    // Get current item data first
    const { data: currentItem, error: itemFetchError } = await supabase
      .from('items')
      .select('*')
      .eq('upc', transaction.upc)
      .single()

    if (itemFetchError) {
      console.error('❌ Failed to fetch current item:', itemFetchError)
      throw new Error(`Failed to fetch current item: ${itemFetchError.message}`)
    }

    // Add previous_quantity to transaction
    const transactionWithPrevious = {
      ...transaction,
      previous_quantity: currentItem.quantity || 0
    }

    // Insert transaction record
    const { data: transactionData, error: transactionError } = await supabase
      .from('transactions')
      .insert([transactionWithPrevious])
      .select()
      .single()

    if (transactionError) {
      console.error('❌ Supabase transaction create failed:', transactionError)
      throw new Error(`Failed to create transaction: ${transactionError.message}`)
    }

    console.log('✅ Transaction created successfully:', transactionData)

    // Update the item's quantity and related fields
    const itemUpdates: Partial<Item> = {
      quantity: transaction.new_quantity,
      updated_at: new Date().toISOString()
    }

    // Note: date_last_counted is handled by database trigger for count transactions

    const { data: updatedItem, error: itemError } = await supabase
      .from('items')
      .update(itemUpdates)
      .eq('upc', transaction.upc)
      .select()
      .single()

    if (itemError) {
      console.error('❌ Failed to update item quantity:', itemError)
      throw new Error(`Failed to update item quantity: ${itemError.message}`)
    }

    console.log('✅ Item updated successfully:', updatedItem)

    // Check and send email alerts after successful transaction
    try {
      if (typeof window === 'undefined') {
        // Server-side: directly import and run email alerts
        console.log('📧 Server-side: Checking email alerts directly...')
        const { emailAlerts } = await import('@/lib/email')
        await emailAlerts.checkAndSendAlerts(updatedItem)
      } else {
        // Client-side: call the API endpoint
        console.log('📧 Client-side: Triggering email alert check via API...')

        const response = await fetch('/api/check-alerts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            itemUpc: updatedItem.upc
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Email alert check completed:', result)
        } else {
          const error = await response.json()
          console.error('⚠️ Email alert API failed:', error)
        }
      }
    } catch (error) {
      // Don't fail the transaction if email alerts fail
      console.error('⚠️ Email alert check failed (transaction still successful):', error)
    }

    return {
      transaction: transactionData,
      updatedItem: updatedItem
    }
  }
}
