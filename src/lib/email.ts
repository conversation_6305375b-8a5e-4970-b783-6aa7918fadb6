import nodemailer from 'nodemailer'
import { supabase, Item } from './supabase'

// Email configuration
const EMAIL_CONFIG = {
  from: process.env.EMAIL_FROM || '<EMAIL>',
  to: process.env.EMAIL_TO || '<EMAIL>',
  defaultCycleCountDays: parseInt(process.env.CYCLE_COUNT_DAYS || '30'),
  // SMTP Configuration
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER || process.env.EMAIL_FROM,
      pass: process.env.SMTP_PASS || 'your-app-password'
    }
  }
}

// Create reusable transporter object using SMTP transport
const createTransporter = () => {
  return nodemailer.createTransport({
    host: EMAIL_CONFIG.smtp.host,
    port: EMAIL_CONFIG.smtp.port,
    secure: EMAIL_CONFIG.smtp.secure,
    auth: {
      user: EMAIL_CONFIG.smtp.auth.user,
      pass: EMAIL_CONFIG.smtp.auth.pass,
    },
  })
}

// Interface for email notification tracking
export interface EmailNotification {
  id?: number
  upc: string
  notification_type: 'low_stock' | 'cycle_count'
  email_sent_at: string
  email_status?: string
  email_content?: object
}

// Email notification API
export const emailNotificationsApi = {
  async create(notification: Omit<EmailNotification, 'id'>): Promise<EmailNotification> {
    const { data, error } = await supabase
      .from('email_notifications')
      .insert([notification])
      .select()
      .single()

    if (error) {
      console.error('❌ Failed to create email notification record:', error)
      throw new Error(`Failed to create email notification: ${error.message}`)
    }

    return data
  },

  async getLastNotification(itemUpc: string, type: 'low_stock' | 'cycle_count'): Promise<EmailNotification | null> {
    const { data, error } = await supabase
      .from('email_notifications')
      .select('*')
      .eq('upc', itemUpc)
      .eq('notification_type', type)
      .order('email_sent_at', { ascending: false })
      .limit(1)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows found
        return null
      }
      console.error('❌ Failed to get last notification:', error)
      throw new Error(`Failed to get last notification: ${error.message}`)
    }

    return data
  }
}

// Email template functions
export const emailTemplates = {
  lowStockAlert(item: Item): { subject: string; text: string; html: string } {
    const subject = `Low Stock Alert for ${item.description}`
    
    const text = `The item ${item.description} (UPC: ${item.upc}) is low in stock.
Current Quantity: ${item.quantity || 0}
Minimum Required: ${item.minimum || 'Not set'}
Product Image: ${item.product_image || 'No image available'}
Purchase Link: ${item.purchase_link || 'No link available'}
Suggested Brand/Model: ${item.suggested_brand_model || 'Not specified'}
Suggested Purchase Quantity: ${item.suggested_purchase_qty || 'Not specified'}
Notes: ${item.notes || 'No additional notes.'}`

    const html = `
      <h2>🚨 Low Stock Alert</h2>
      <p>The item <strong>${item.description}</strong> (UPC: ${item.upc}) is low in stock.</p>

      ${item.product_image ? `
      <div style="text-align: center; margin: 20px 0;">
        <img src="${item.product_image}" alt="${item.description}" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;" />
      </div>
      ` : ''}

      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Current Quantity:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.quantity || 0}</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Minimum Required:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.minimum || 'Not set'}</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Suggested Brand/Model:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.suggested_brand_model || 'Not specified'}</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Suggested Purchase Quantity:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.suggested_purchase_qty || 'Not specified'}</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Notes:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.notes || 'No additional notes.'}</td>
        </tr>
      </table>

      ${item.purchase_link ? `
      <div style="text-align: center; margin: 20px 0;">
        <a href="${item.purchase_link}" target="_blank" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">🛒 Purchase This Item</a>
      </div>
      ` : ''}

      <p><em>This alert was generated automatically by the ISC Inventory Management System.</em></p>
    `

    return { subject, text, html }
  },

  cycleCountAlert(item: Item): { subject: string; text: string; html: string } {
    const subject = `Cycle Count Alert for ${item.description}`
    
    const text = `The item ${item.description} (UPC: ${item.upc}) needs counted.
Current Quantity: ${item.quantity || 0}
Product Image: ${item.product_image || 'No image available'}
Suggested Brand/Model: ${item.suggested_brand_model || 'Not specified'}
Notes: ${item.notes || 'No additional notes.'}`

    const html = `
      <h2>📊 Cycle Count Alert</h2>
      <p>The item <strong>${item.description}</strong> (UPC: ${item.upc}) needs counted.</p>

      ${item.product_image ? `
      <div style="text-align: center; margin: 20px 0;">
        <img src="${item.product_image}" alt="${item.description}" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;" />
      </div>
      ` : ''}

      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Current Quantity:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.quantity || 0}</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Suggested Brand/Model:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.suggested_brand_model || 'Not specified'}</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Notes:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${item.notes || 'No additional notes.'}</td>
        </tr>
      </table>

      <p><em>This alert was generated automatically by the ISC Inventory Management System.</em></p>
    `

    return { subject, text, html }
  }
}

// Alert checking functions
export const alertCheckers = {
  async shouldSendLowStockAlert(item: Item): Promise<boolean> {
    console.log(`🔍 Checking low stock alert for ${item.description}:`)
    console.log(`  - Quantity: ${item.quantity || 0}`)
    console.log(`  - Minimum: ${item.minimum || 'not set'}`)

    // Check if item has minimum set and current quantity is at or below minimum
    if (!item.minimum || (item.quantity || 0) > item.minimum) {
      console.log(`  - ❌ No alert needed: quantity (${item.quantity || 0}) > minimum (${item.minimum || 'not set'})`)
      return false
    }

    console.log(`  - ✅ Low stock condition met: quantity (${item.quantity || 0}) <= minimum (${item.minimum})`)

    // Check if we've already sent a low stock alert recently (within 24 hours)
    try {
      const lastNotification = await emailNotificationsApi.getLastNotification(item.upc, 'low_stock')
      if (lastNotification) {
        const lastSentTime = new Date(lastNotification.email_sent_at)
        const now = new Date()
        const hoursSinceLastAlert = (now.getTime() - lastSentTime.getTime()) / (1000 * 60 * 60)

        // Don't send another alert if one was sent within the last 24 hours
        if (hoursSinceLastAlert < 24) {
          console.log(`  - ⏰ Low stock alert for ${item.description} was already sent ${hoursSinceLastAlert.toFixed(1)} hours ago`)
          return false
        } else {
          console.log(`  - ✅ Last alert was ${hoursSinceLastAlert.toFixed(1)} hours ago, can send new alert`)
        }
      } else {
        console.log(`  - ✅ No previous low stock alerts found`)
      }
    } catch (error) {
      console.error('  - ❌ Error checking last low stock notification:', error)
      // If we can't check, err on the side of not sending to avoid spam
      return false
    }

    console.log(`  - ✅ Should send low stock alert: YES`)
    return true
  },

  async shouldSendCycleCountAlert(item: Item): Promise<boolean> {
    // Get the cycle count interval (use item-specific or default)
    const cycleCountDays = item.count_interval_days || EMAIL_CONFIG.defaultCycleCountDays

    // Check if date_last_counted is older than the cycle count interval
    if (!item.date_last_counted) {
      // If never counted, check if item is old enough to warrant counting
      const createdDate = new Date(item.created_at)
      const now = new Date()
      const daysSinceCreated = (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24)

      if (daysSinceCreated < cycleCountDays) {
        return false
      }
    } else {
      const lastCountedDate = new Date(item.date_last_counted)
      const now = new Date()
      const daysSinceLastCount = (now.getTime() - lastCountedDate.getTime()) / (1000 * 60 * 60 * 24)

      if (daysSinceLastCount < cycleCountDays) {
        return false
      }
    }

    // Check if we've already sent a cycle count alert recently (within 7 days)
    try {
      const lastNotification = await emailNotificationsApi.getLastNotification(item.upc, 'cycle_count')
      if (lastNotification) {
        const lastSentTime = new Date(lastNotification.email_sent_at)
        const now = new Date()
        const daysSinceLastAlert = (now.getTime() - lastSentTime.getTime()) / (1000 * 60 * 60 * 24)

        // Don't send another alert if one was sent within the last 7 days
        if (daysSinceLastAlert < 7) {
          console.log(`⏰ Cycle count alert for ${item.description} was already sent ${daysSinceLastAlert.toFixed(1)} days ago`)
          return false
        }
      }
    } catch (error) {
      console.error('❌ Error checking last cycle count notification:', error)
      // If we can't check, err on the side of not sending to avoid spam
      return false
    }

    return true
  }
}

// Main email sending functions
export const emailAlerts = {
  async sendLowStockAlert(item: Item): Promise<boolean> {
    try {
      console.log(`📧 Sending low stock alert for ${item.description}`)

      const template = emailTemplates.lowStockAlert(item)
      const transporter = createTransporter()

      const mailOptions = {
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.to,
        subject: template.subject,
        text: template.text,
        html: template.html,
      }

      const info = await transporter.sendMail(mailOptions)
      console.log('✅ Low stock email sent successfully:', info.messageId)

      // Record the notification in the database
      await emailNotificationsApi.create({
        upc: item.upc,
        notification_type: 'low_stock',
        email_sent_at: new Date().toISOString(),
        email_status: 'sent',
        email_content: {
          subject: template.subject,
          messageId: info.messageId
        }
      })

      return true
    } catch (error) {
      console.error('❌ Error sending low stock alert:', error)
      return false
    }
  },

  async sendCycleCountAlert(item: Item): Promise<boolean> {
    try {
      console.log(`📧 Sending cycle count alert for ${item.description}`)

      const template = emailTemplates.cycleCountAlert(item)
      const transporter = createTransporter()

      const mailOptions = {
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.to,
        subject: template.subject,
        text: template.text,
        html: template.html,
      }

      const info = await transporter.sendMail(mailOptions)
      console.log('✅ Cycle count email sent successfully:', info.messageId)

      // Record the notification in the database
      await emailNotificationsApi.create({
        upc: item.upc,
        notification_type: 'cycle_count',
        email_sent_at: new Date().toISOString(),
        email_status: 'sent',
        email_content: {
          subject: template.subject,
          messageId: info.messageId
        }
      })

      return true
    } catch (error) {
      console.error('❌ Error sending cycle count alert:', error)
      return false
    }
  },

  async checkAndSendAlerts(item: Item): Promise<void> {
    console.log(`📧 Checking email alerts for item: ${item.description} (UPC: ${item.upc})`)

    try {
      // Check and send low stock alert
      console.log(`🔍 Checking low stock alert...`)
      if (await alertCheckers.shouldSendLowStockAlert(item)) {
        console.log(`📧 Sending low stock alert...`)
        await this.sendLowStockAlert(item)
      } else {
        console.log(`⏭️ Skipping low stock alert`)
      }

      // Check and send cycle count alert
      console.log(`🔍 Checking cycle count alert...`)
      if (await alertCheckers.shouldSendCycleCountAlert(item)) {
        console.log(`📧 Sending cycle count alert...`)
        await this.sendCycleCountAlert(item)
      } else {
        console.log(`⏭️ Skipping cycle count alert`)
      }
    } catch (error) {
      console.error('❌ Error checking and sending alerts:', error)
    }

    console.log(`✅ Email alert check completed for ${item.description}`)
  }
}
