// SMTP Testing Utility
import nodemailer from 'nodemailer'

// Test SMTP configuration
export const testSMTPConnection = async () => {
  const config = {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || process.env.EMAIL_FROM,
      pass: process.env.SMTP_PASS || 'your-app-password'
    }
  }

  console.log('🧪 Testing SMTP Configuration...')
  console.log('Host:', config.host)
  console.log('Port:', config.port)
  console.log('Secure:', config.secure)
  console.log('User:', config.auth.user)
  console.log('Pass:', config.auth.pass ? '***hidden***' : 'NOT SET')

  try {
    const transporter = nodemailer.createTransporter(config)
    
    // Verify connection
    console.log('🔗 Verifying SMTP connection...')
    await transporter.verify()
    console.log('✅ SMTP connection verified successfully!')
    
    return { success: true, config }
  } catch (error) {
    console.error('❌ SMTP connection failed:', error)
    return { success: false, error, config }
  }
}

// Send a test email
export const sendTestEmail = async () => {
  const config = {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || process.env.EMAIL_FROM,
      pass: process.env.SMTP_PASS || 'your-app-password'
    }
  }

  const emailFrom = process.env.EMAIL_FROM || '<EMAIL>'
  const emailTo = process.env.EMAIL_TO || '<EMAIL>'

  try {
    const transporter = nodemailer.createTransporter(config)
    
    const mailOptions = {
      from: emailFrom,
      to: emailTo,
      subject: '🧪 Test Email from Inventory System',
      text: `This is a test email from the ISC Inventory Management System.

Configuration Test Results:
- SMTP Host: ${config.host}
- SMTP Port: ${config.port}
- Secure: ${config.secure}
- From: ${emailFrom}
- To: ${emailTo}

If you received this email, your SMTP configuration is working correctly!

Timestamp: ${new Date().toISOString()}`,
      html: `
        <h2>🧪 Test Email from Inventory System</h2>
        <p>This is a test email from the <strong>ISC Inventory Management System</strong>.</p>
        
        <h3>Configuration Test Results:</h3>
        <ul>
          <li><strong>SMTP Host:</strong> ${config.host}</li>
          <li><strong>SMTP Port:</strong> ${config.port}</li>
          <li><strong>Secure:</strong> ${config.secure}</li>
          <li><strong>From:</strong> ${emailFrom}</li>
          <li><strong>To:</strong> ${emailTo}</li>
        </ul>
        
        <p style="color: green;"><strong>✅ If you received this email, your SMTP configuration is working correctly!</strong></p>
        
        <hr>
        <p style="font-size: 12px; color: #666;">
          <strong>Timestamp:</strong> ${new Date().toISOString()}<br>
          <strong>System:</strong> ISC Inventory Management System
        </p>
      `
    }

    console.log('📧 Sending test email...')
    const info = await transporter.sendMail(mailOptions)
    console.log('✅ Test email sent successfully!')
    console.log('Message ID:', info.messageId)
    
    return { success: true, messageId: info.messageId, config }
  } catch (error) {
    console.error('❌ Failed to send test email:', error)
    return { success: false, error, config }
  }
}

// Complete SMTP test suite
export const runSMTPTests = async () => {
  console.log('🚀 Running SMTP Test Suite...\n')
  
  // Test 1: Connection verification
  console.log('=== Test 1: SMTP Connection ===')
  const connectionTest = await testSMTPConnection()
  console.log('')
  
  if (!connectionTest.success) {
    console.log('❌ Connection test failed. Skipping email test.')
    return { connectionTest, emailTest: null }
  }
  
  // Test 2: Send test email
  console.log('=== Test 2: Send Test Email ===')
  const emailTest = await sendTestEmail()
  console.log('')
  
  // Summary
  console.log('=== Test Summary ===')
  console.log(`Connection Test: ${connectionTest.success ? '✅ PASSED' : '❌ FAILED'}`)
  console.log(`Email Test: ${emailTest.success ? '✅ PASSED' : '❌ FAILED'}`)
  
  if (connectionTest.success && emailTest.success) {
    console.log('\n🎉 All tests passed! Your SMTP configuration is working correctly.')
    console.log('You should receive a test email shortly.')
  } else {
    console.log('\n⚠️ Some tests failed. Check the error messages above.')
    console.log('Common issues:')
    console.log('- Gmail: Make sure you\'re using an App Password, not your regular password')
    console.log('- Microsoft: Verify SMTP access is enabled by your IT department')
    console.log('- Check your .env.local file for correct SMTP settings')
  }
  
  return { connectionTest, emailTest }
}

// Environment variable checker
export const checkEmailEnvironment = () => {
  console.log('🔍 Checking Email Environment Variables...\n')
  
  const requiredVars = [
    'EMAIL_FROM',
    'EMAIL_TO', 
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_USER',
    'SMTP_PASS'
  ]
  
  const optionalVars = [
    'SMTP_SECURE',
    'CYCLE_COUNT_DAYS'
  ]
  
  let allGood = true
  
  console.log('Required Variables:')
  requiredVars.forEach(varName => {
    const value = process.env[varName]
    const status = value ? '✅' : '❌'
    const displayValue = varName.includes('PASS') ? (value ? '***hidden***' : 'NOT SET') : (value || 'NOT SET')
    console.log(`  ${status} ${varName}: ${displayValue}`)
    if (!value) allGood = false
  })
  
  console.log('\nOptional Variables:')
  optionalVars.forEach(varName => {
    const value = process.env[varName]
    const status = value ? '✅' : '⚪'
    console.log(`  ${status} ${varName}: ${value || 'NOT SET (using default)'}`)
  })
  
  console.log(`\nEnvironment Check: ${allGood ? '✅ PASSED' : '❌ FAILED'}`)
  
  if (!allGood) {
    console.log('\n⚠️ Missing required environment variables. Please update your .env.local file.')
  }
  
  return allGood
}
