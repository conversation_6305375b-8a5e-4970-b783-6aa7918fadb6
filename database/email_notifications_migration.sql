-- Create email_notifications table for tracking sent alerts
CREATE TABLE IF NOT EXISTS email_notifications (
  id SERIAL PRIMARY KEY,
  item_upc VARCHAR(255) NOT NULL,
  notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN ('low_stock', 'cycle_count')),
  sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Foreign key constraint to items table
  CONSTRAINT fk_email_notifications_item_upc 
    FOREIGN KEY (item_upc) 
    REFERENCES items(upc) 
    ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_notifications_item_upc ON email_notifications(item_upc);
CREATE INDEX IF NOT EXISTS idx_email_notifications_type ON email_notifications(notification_type);
CREATE INDEX IF NOT EXISTS idx_email_notifications_sent_at ON email_notifications(sent_at);
CREATE INDEX IF NOT EXISTS idx_email_notifications_item_type ON email_notifications(item_upc, notification_type);

-- Add RLS (Row Level Security) policies if needed
ALTER TABLE email_notifications ENABLE ROW LEVEL SECURITY;

-- Allow all operations for authenticated users (adjust as needed for your security requirements)
CREATE POLICY "Allow all operations for authenticated users" ON email_notifications
  FOR ALL USING (auth.role() = 'authenticated');

-- Allow anonymous read access (adjust as needed)
CREATE POLICY "Allow anonymous read access" ON email_notifications
  FOR SELECT USING (true);

-- Allow anonymous insert access (adjust as needed)
CREATE POLICY "Allow anonymous insert access" ON email_notifications
  FOR INSERT WITH CHECK (true);

-- Comments for documentation
COMMENT ON TABLE email_notifications IS 'Tracks email notifications sent for inventory alerts';
COMMENT ON COLUMN email_notifications.item_upc IS 'UPC of the item the notification was sent for';
COMMENT ON COLUMN email_notifications.notification_type IS 'Type of notification: low_stock or cycle_count';
COMMENT ON COLUMN email_notifications.sent_at IS 'Timestamp when the email was sent';
COMMENT ON COLUMN email_notifications.created_at IS 'Timestamp when the record was created';
